package com.zsm.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.common.exception.BusinessException;
import com.zsm.config.HangChuangConfig;
import com.zsm.constant.DrugDictAccountConstant;
import com.zsm.constant.InpatientAccountConstant;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.YsfStoTcTask;
import com.zsm.entity.YsfStoTcTaskSub;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.HisDrugCatalogResponse;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.dto.OutpatientPrescriptionQueryDto;
import com.zsm.model.enums.SdDpsEnum;
import com.zsm.model.enums.SyncAccountEnum;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.saas.request.*;
import com.zsm.model.saas.response.*;
import com.zsm.model.vo.*;
import com.zsm.utils.HttpRequestUtil;
import com.zsm.utils.SaasHttpUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 太和人医his杭创通用服务类
 *
 * <AUTHOR>
 * @date 2025/6/9 12:15
 */
@Slf4j
@Service
public class HangChuangService {

    @Resource
    private HangChuangConfig hangChuangConfig;

    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;

    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private YsfStoTcTaskSubService ysfStoTcTaskSubService;
    @Resource
    private DataPersistenceService dataPersistenceService;

    /**
     * 门诊处方查询
     * 参考阜阳人医门诊处方查询业务流程，使用HttpRequestUtil调用接口
     * 流程：根据cardType类型确定查询方式：
     * 1-直接使用patientId查询；
     * 2-身份证号查询（先获取患者ID，再查询）；
     * 3-卡号查询（先获取患者ID，再查询）；
     * 4-处方号(cfxh)查询（直接使用cfxh参数查询）
     *
     * @param queryDto 查询dto
     * @return {@link ApiResult }<{@link List }<{@link OutpatientPrescriptionResponseVo.PrescriptionItem }>>
     */
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(OutpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询门诊处方发药数据，查询参数：{}", JSONUtil.toJsonStr(queryDto));

            // 参数校验
            if (queryDto == null) {
                queryDto = new OutpatientPrescriptionQueryDto();
            }

            // 验证cardType和对应参数
            validateQueryParams(queryDto);

            // Step 1: 根据cardType确定患者ID获取方式
            String patientId = getPatientIdByQueryType(queryDto);

            // Step 2: 构建门诊发药明细查询参数
            Map<String, Object> requestParams = buildOutpatientRequestParams(queryDto, patientId);

            // Step 3: 获取门诊发药明细接口URL
            String requestUrl = hangChuangConfig.getOutpatientDispenseDetailUrl();

            // Step 4: 发起HTTP请求查询门诊发药明细
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "查询门诊发药明细");

            // Step 5: 解析响应结果
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList = parseOutpatientResponse(responseBody);

            log.info("his接口查询门诊发药明细成功，返回{}条数据", resultList.size());

            // Step 6: 处理追溯码库存信息（参考阜阳人医逻辑）
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            if (userInfo != null && !resultList.isEmpty()) {
                handleTracCodgStoreForOutpatient(resultList, userInfo.getAuthorization());
            } else {
                log.warn("未获取到SaaS用户信息，跳过追溯码库存处理");
            }

            // Step 7: 处理任务状态和数据过滤（参考阜阳人医门诊处方查询逻辑）
            filterByTaskStatusForOutpatient(resultList);

            // Step 8: 异步保存到nhsa_3505表
            if (!resultList.isEmpty()) {
                nhsa3505Service.save3505Async(resultList);
            }

            log.info("查询门诊处方发药数据完成，最终返回记录数：{}", resultList.size());
            return ApiResult.success(resultList);

        } catch (Exception e) {
            log.error("查询门诊处方发药数据异常", e);
            return ApiResult.error("查询门诊处方发药数据失败：" + e.getMessage());
        }
    }

    public ApiResult<List<InPatientDispenseRecordVo>> queryInpatientDispenseRecord(InpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询住院发药记录数据，查询参数：{}", JSONUtil.toJsonStr(queryDto));

            // 参数校验
            if (queryDto == null) {
                queryDto = new InpatientPrescriptionQueryDto();
            }

            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams(queryDto);

            // 获取请求URL
            String requestUrl = hangChuangConfig.getInpatientDispenseRecordUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "查询住院发药记录");

            // 解析响应结果
            List<InPatientDispenseRecordVo> resultList = HttpRequestUtil.parseResponseToList(responseBody, InPatientDispenseRecordVo.class);

            log.info("查询住院发药记录数据完成，最终返回记录数：{}", resultList.size());
            return ApiResult.success(resultList);

        } catch (Exception e) {
            log.error("查询住院发药记录数据异常", e);
            return ApiResult.error("查询住院发药记录数据失败：" + e.getMessage());
        }
    }

    /**
     * 查询住院处方发药
     * 参考阜阳人医门诊处方查询业务流程，包含任务状态处理和数据过滤
     *
     * @param queryDto 查询dto
     * @return {@link ApiResult }<{@link List }<{@link InPatientDispenseDetailBindScatteredVo }>>
     */
    public ApiResult<List<InPatientDispenseDetailBindScatteredVo>> queryInpatientPrescription(InpatientPrescriptionQueryDto queryDto) {

        try {
            log.info("开始查询住院处方发药数据，查询参数：{}", JSONUtil.toJsonStr(queryDto));

            // 参数校验
            if (queryDto == null) {
                queryDto = new InpatientPrescriptionQueryDto();
            }

            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams(queryDto);

            // 获取请求URL
            String requestUrl = hangChuangConfig.getInpatientDispenseDetailUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "查询住院处方发药");

            // 解析响应结果
            List<InPatientDispenseDetailVo> originalList = HttpRequestUtil.parseResponseToList(responseBody, InPatientDispenseDetailVo.class);

            log.info("杭创接口查询成功，返回{}条数据", originalList.size());

            // 转换为扩展VO并处理追溯码库存信息
            List<InPatientDispenseDetailBindScatteredVo> resultList = convertAndHandleTracCodgStore(originalList);

            // 处理追溯码库存信息（需要SaaS用户信息）
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            if (userInfo != null) {
                handleTracCodgStoreForInpatient(resultList, userInfo.getAuthorization());
            } else {
                log.warn("未获取到SaaS用户信息，跳过追溯码库存处理");
            }

            // 处理任务状态和数据过滤（参考阜阳人医门诊处方查询逻辑）
            filterByTaskStatus(resultList, queryDto);

            // 异步保存到nhsa_3505表
            if (!resultList.isEmpty()) {
                // 异步保存住院发药数据到nhsa_3505表
                nhsa3505Service.saveInpatientDataToNhsa3505Async(resultList);
            }

            log.info("查询住院处方发药数据完成，最终返回记录数：{}", resultList.size());
            return ApiResult.success(resultList);

        } catch (Exception e) {
            log.error("查询住院处方发药数据异常", e);
            return ApiResult.error("查询住院处方发药数据失败：" + e.getMessage());
        }
    }

    /**
     * 转换为扩展VO并处理追溯码库存信息
     * 参考阜阳人医的handleTracCodgStoreCydy方法逻辑
     *
     * @param originalList 原始住院发药明细列表
     * @return 扩展的住院发药明细列表（包含拆零信息）
     */
    private List<InPatientDispenseDetailBindScatteredVo> convertAndHandleTracCodgStore(List<InPatientDispenseDetailVo> originalList) {
        if (originalList == null || originalList.isEmpty()) {
            return new ArrayList<>();
        }

        List<InPatientDispenseDetailBindScatteredVo> extendedList = new ArrayList<>();

        // 转换数据类型
        for (InPatientDispenseDetailVo original : originalList) {
            InPatientDispenseDetailBindScatteredVo extended = new InPatientDispenseDetailBindScatteredVo();
            // 复制基础字段
            copyBasicFields(original, extended);
            extendedList.add(extended);
        }


        return extendedList;
    }

    /**
     * 复制基础字段从原始VO到扩展VO
     *
     * @param source 原始VO
     * @param target 目标扩展VO
     */
    private void copyBasicFields(InPatientDispenseDetailVo source, InPatientDispenseDetailBindScatteredVo target) {
        // 复制所有基础字段
        target.setRecordId(source.getRecordId());
        target.setRecordDetailId(source.getRecordDetailId());
        target.setOriDetailId(source.getOriDetailId());
        target.setIdFee(source.getIdFee());
        target.setNaFee(source.getNaFee());
        target.setSdClassify(source.getSdClassify());
        target.setFgDps(source.getFgDps());
        target.setSendFlag(source.getSendFlag());
        target.setSendTime(source.getSendTime());
        target.setRtalDocno(source.getRtalDocno());
        target.setStooutNo(source.getStooutNo());
        target.setPatWardId(source.getPatWardId());
        target.setPatWardName(source.getPatWardName());
        target.setFyyf(source.getFyyf());
        target.setDeptId(source.getDeptId());
        target.setPharCertno(source.getPharCertno());
        target.setPharName(source.getPharName());
        target.setPharPracCertNo(source.getPharPracCertNo());
        target.setSelRetnTime(source.getSelRetnTime());
        target.setHisDrugCode(source.getHisDrugCode());
        target.setMedListCodg(source.getMedListCodg());
        target.setSpec(source.getSpec());
        target.setProdentpName(source.getProdentpName());
        target.setFixmedinsHilistId(source.getFixmedinsHilistId());
        target.setFixmedinsHilistName(source.getFixmedinsHilistName());
        target.setManuLotnum(source.getManuLotnum());
        target.setManuDate(source.getManuDate());
        target.setExpyEnd(source.getExpyEnd());
        target.setBchno(source.getBchno());
        target.setRxFlag(source.getRxFlag());
        target.setTrdnFlag(source.getTrdnFlag());
        target.setHisDosUnit(source.getHisDosUnit());
        target.setHisPacUnit(source.getHisPacUnit());
        target.setHisConRatio(source.getHisConRatio());
        target.setFixmedinsBchno(source.getFixmedinsBchno());
        target.setPatInHosId(source.getPatInHosId());
        target.setMdtrtSn(source.getMdtrtSn());
        target.setPsnName(source.getPsnName());
        target.setBedNo(source.getBedNo());
        target.setMdtrtSetlType(source.getMdtrtSetlType());
        target.setOrderId(source.getOrderId());
        target.setPrscDrCertno(source.getPrscDrCertno());
        target.setPrscDrName(source.getPrscDrName());
        target.setSelRetnCnt(source.getSelRetnCnt());
        target.setSelRetnUnit(source.getSelRetnUnit());
        target.setCfxh(source.getCfxh());
        target.setCfmxxh(source.getCfmxxh());
    }

    /**
     * 处理住院药品的追溯码库存信息
     * 参考阜阳人医的handleTracCodgStoreCydy方法逻辑
     *
     * @param extendedList 扩展的住院发药明细列表
     * @param token        令牌
     */
    public List<InPatientDispenseDetailBindScatteredVo> handleTracCodgStoreForInpatient(List<InPatientDispenseDetailBindScatteredVo> extendedList, String token) {
        try {
            // 1. 构建查询药品追溯信息的请求列表
            List<QueryTracDrugRequest> queryTracDrugRequestList = extendedList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCfmxxh()) && item.getSelRetnCnt() != null)
                    .map(item -> QueryTracDrugRequest.builder()
                            .cfxh(item.getCfxh())
                            .cfmxxh(item.getCfmxxh())
                            .drugCode(item.getFixmedinsHilistId())
                            .dispCnt(item.getSelRetnCnt())
                            .build())
                    .collect(Collectors.toList());

            if (queryTracDrugRequestList.isEmpty()) {
                log.info("没有需要查询追溯信息的药品");
                return extendedList;
            }

            // 调用SaaS接口批量查询药品追溯信息
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);

            // 2. 处理追溯标志和转换比
            List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(item.getCfmxxh());
                    // 设置追溯标志（住院药品使用trdnFlag字段）
                    item.setTrdnFlag(queryTracDrug.getIsTrac());
                    // 设置HIS转换比例
                    item.setHisConRatio(String.valueOf(queryTracDrug.getConRatio()));

                    // 如果药品需要追溯，收集需要查询库存的药品
                    if ("1".equals(queryTracDrug.getIsTrac())) {
                        GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder()
                                .cfxh(item.getCfxh())
                                .cfmxxh(item.getCfmxxh())
                                .dispCnt(item.getSelRetnCnt() != null ? item.getSelRetnCnt() : 0)
                                .drugCode(item.getFixmedinsHilistId())
                                .build();
                        tracDataList.add(build);
                    }
                }
            }

            // 3. 批量获取追溯码库存信息
            Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
            if (!tracDataList.isEmpty()) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token,
                        GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }

            // 4. 将库存信息填充回药品对象
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if ("1".equals(item.getTrdnFlag()) && tracCodgStoreMap.containsKey(item.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(item.getCfmxxh());
                    item.setDrugCode(tracCodgStoreData.getDrugCode());
                    item.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    item.setDispCnt(tracCodgStoreData.getDispCnt());
                    item.setCurrNum(tracCodgStoreData.getCurrNum());
                    item.setTracCodgStore(tracCodgStoreData);
                }
            }

            log.info("住院药品追溯码库存信息处理完成，共处理{}条数据，其中{}条需要追溯", extendedList.size(), tracDataList.size());

        } catch (Exception e) {
            log.error("处理住院药品追溯码库存信息异常", e);
            // 不抛出异常，避免影响主流程
        }
        return extendedList;
    }

    /**
     * 处理任务状态和数据过滤
     * 参考阜阳人医门诊处方查询中的任务状态处理逻辑
     * 过滤掉已完成扫码任务的药品信息，防止后续业务重复上传造成脏数据
     *
     * @param resultList 住院发药明细列表
     */
    private void filterByTaskStatus(List<InPatientDispenseDetailBindScatteredVo> resultList, InpatientPrescriptionQueryDto queryDt) {
        String outPresId = queryDt.getRecordId() + "-" + queryDt.getPatWardId();
        if (StringUtils.isEmpty(outPresId)) {
            log.warn("处方ID为空，无法查询关联任务");
            return;
        }

        // 1. 查询是否存在已完成且未删除的任务
        LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
        completedTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode()) // 已完成
                .eq(YsfStoTcTask::getDelFlag, "0"); // 未删除
        Long completedTaskCount = ysfStoTcTaskMapper.selectCount(completedTaskQuery);

        // 2. 如果存在已完成的任务，则过滤掉该处方数据


        // 3. 如果不存在已完成的任务，则查询最新的待处理或已失效任务
        LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
        latestTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode())) // 待处理或已失效
                .eq(YsfStoTcTask::getDelFlag, "0") // 未删除
                .orderByDesc(YsfStoTcTask::getIdTask)
                .last("limit 1"); // 按创建时间降序

        YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

        // 4. 如果找到这样的任务，则在处方对象中追加任务相关字段（需要扩展VO添加对应字段）
        if (latestTask != null) {
            // 注意：这里需要确保InPatientDispenseDetailBindScatteredVo有对应的任务字段
            // 如果没有，可以考虑添加或通过其他方式处理
            log.debug("处方[{}]找到待处理任务，任务ID：{}, 状态：{}", outPresId, latestTask.getIdTask(), latestTask.getFgStatus());

            final List<YsfStoTcTaskSub> taskSubList = ysfStoTcTaskSubService.lambdaQuery()
                    .eq(YsfStoTcTaskSub::getIdTask, latestTask.getIdTask()).list();

            // 处理每一条处方明细信息
            resultList.forEach(detail -> {
                // 添加药品字典信息

                // 如果处方明细有关联的任务，查询追溯码信息
                if (latestTask.getIdTask() != null && StringUtils.isNotEmpty(detail.getRecordDetailId())) {
                    try {
                        // 查询该明细对应的任务明细记录
                        YsfStoTcTaskSub taskSub = taskSubList.stream()
                                .filter(item -> item.getCfmxxh().equals(detail.getRecordDetailId())).findFirst().orElse(null);

                        if (taskSub != null) {
                            // 设置追溯码相关信息
                            detail.setTaskIdSubDps(taskSub.getIdSub() != null ?
                                    String.valueOf(taskSub.getIdSub()) : null);
                            detail.setTaskDrugtracinfoDps(taskSub.getDrugtracinfo());
                            detail.setTaskFgScannedDps(taskSub.getFgScanned());
                            // 扫描时间格式化
                            if (taskSub.getScanTime() != null) {
                                detail.setTaskDetailScanTimeDps(taskSub.getScanTime()
                                        .toString());
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询药品追溯码任务明细异常: {}", e.getMessage(), e);
                    }
                }
            });
        }

    }

    /**
     * 安全地解析整数
     *
     * @param value 字符串值
     * @return 整数值，解析失败时返回0
     */
    private Integer parseIntegerSafely(String value) {
        try {
            return StringUtils.isNotEmpty(value) ? Integer.valueOf(value) : 0;
        } catch (NumberFormatException e) {
            log.warn("解析整数失败，值：{}，返回默认值0", value);
            return 0;
        }
    }

    /**
     * 验证查询参数
     *
     * @param queryDto 查询参数
     */
    private void validateQueryParams(OutpatientPrescriptionQueryDto queryDto) {
        if (StrUtil.isBlank(queryDto.getCardType())) {
            throw new BusinessException("查询类型(cardType)不能为空");
        }

        switch (queryDto.getCardType()) {
            case "1":
                if (StrUtil.isBlank(queryDto.getPatient_id())) {
                    throw new BusinessException("cardType=1时，患者ID(patient_id)不能为空");
                }
                break;
            case "2":
            case "3":
                if (StrUtil.isBlank(queryDto.getCardNo())) {
                    throw new BusinessException("cardType=2或3时，卡号(cardNo)不能为空");
                }
                break;
            case "4":
            case "5":
                if (StrUtil.isBlank(queryDto.getCfxh())) {
                    throw new BusinessException("cardType=4或5时，处方号(cfxh)不能为空");
                }
                break;
            default:
                throw new BusinessException("不支持的查询类型：" + queryDto.getCardType() + "，支持的类型：1-患者ID查询，2-身份证号查询，3-卡号查询，4-处方号查询，5-处方号查询患者所有处方");
        }
    }

    /**
     * 根据查询类型获取患者ID
     *
     * @param queryDto 查询参数
     * @return 患者ID，找不到时返回null或抛出异常
     */
    private String getPatientIdByQueryType(OutpatientPrescriptionQueryDto queryDto) {

        if (queryDto.getCardType().equals("1")) {
            return queryDto.getPatient_id();
        }

        // cardType=4时，使用cfxh查询，不需要获取患者ID
        if (queryDto.getCardType().equals("4")) {
            return null; // cfxh查询不需要患者ID
        }

        // cardType=5时，先通过cfxh查询处方，从结果中提取患者ID
        if (queryDto.getCardType().equals("5")) {
            return getPatientIdByCfxh(queryDto.getCfxh());
        }

        // 如果是cardType=2或3，通过卡号获取患者ID
        String patientId = getPatientId(queryDto.getCardType(), queryDto.getCardNo());
        if (StrUtil.isBlank(patientId)) {
            throw new BusinessException("未找到该卡号对应的患者信息");
        }
        return patientId;
    }


    /**
     * 通过卡号获取患者ID
     * 调用获取患者信息接口
     *
     * @param cardNo 卡号
     * @return 患者ID，找不到时返回null
     */
    private String getPatientId(String cardType, String cardNo) {
        try {
            log.info("开始通过卡号获取患者ID，卡号：{}", cardNo);

            // 构建查询患者信息的URL（使用GET请求，参数放在URL中）
            String requestUrl = hangChuangConfig.getPatientInfoUrl() +
                    "?queryType=" + cardType + "&queryValue=" + cardNo;

            // 发起GET请求
            String responseBody = HttpRequestUtil.executeGetRequest(requestUrl, "获取患者信息");

            // 解析响应结果
            return parsePatientInfoResponse(responseBody);

        } catch (Exception e) {
            log.error("通过卡号获取患者ID异常", e);
            return null;
        }
    }

    /**
     * 通过处方号获取患者ID
     * 先查询该处方的数据，从结果中提取患者ID
     *
     * @param cfxh 处方号
     * @return 患者ID，找不到时抛出异常
     */
    private String getPatientIdByCfxh(String cfxh) {
        try {
            log.info("开始通过处方号获取患者ID，处方号：{}", cfxh);

            // 构建处方查询参数，只使用cfxh
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("cfxh", cfxh);

            // 获取门诊发药明细接口URL
            String requestUrl = hangChuangConfig.getOutpatientDispenseDetailUrl();

            // 发起HTTP请求查询门诊发药明细
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "通过处方号查询患者ID");

            // 解析响应结果，提取患者ID
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList = parseOutpatientResponse(responseBody);

            if (resultList.isEmpty()) {
                throw new BusinessException("未找到处方号[" + cfxh + "]对应的处方信息");
            }

            // 从第一条数据中提取患者ID
            String patientId = resultList.get(0).getPatient_id();
            if (StrUtil.isBlank(patientId)) {
                throw new BusinessException("处方号[" + cfxh + "]对应的患者ID为空");
            }

            log.info("成功获取处方号[{}]对应的患者ID：{}", cfxh, patientId);
            return patientId;

        } catch (Exception e) {
            log.error("通过处方号获取患者ID异常，处方号：{}", cfxh, e);
            throw new BusinessException("通过处方号获取患者ID失败：" + e.getMessage());
        }
    }

    /**
     * 解析患者信息查询接口的响应
     *
     * @param responseBody 响应体
     * @return 患者ID，找不到时返回null
     */
    private String parsePatientInfoResponse(String responseBody) {
        try {
            // 解析响应结果
            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查返回码
            String resultCode = responseJson.getStr("resultCode");
            if (!"1".equals(resultCode)) {
                log.warn("获取患者信息失败，返回码：{}，消息：{}", resultCode, responseJson.getStr("resultMessage"));
                return null;
            }

            // 获取患者数据
            Object data = responseJson.get("data");
            if (data == null) {
                log.warn("患者信息数据为空");
                return null;
            }

            // 解析患者列表
            List<Map<String, Object>> patientList = (List<Map<String, Object>>) data;
            if (patientList.isEmpty()) {
                log.warn("未找到对应的患者信息");
                return null;
            }

            // 获取第一个患者的ID
            Map<String, Object> patient = patientList.get(0);
            String patientId = (String) patient.get("patientId");

            log.info("成功获取患者ID：{}，患者姓名：{}", patientId, patient.get("clientName"));
            return patientId;

        } catch (Exception e) {
            log.error("解析患者信息响应异常", e);
            return null;
        }
    }

    /**
     * 构建门诊发药明细查询参数
     *
     * @param queryDto  查询DTO
     * @param patientId 患者ID
     * @return 请求参数Map
     */
    private Map<String, Object> buildOutpatientRequestParams(OutpatientPrescriptionQueryDto queryDto, String patientId) {
        Map<String, Object> params = new HashMap<>();

        // 根据cardType构建不同的查询参数
        switch (queryDto.getCardType()) {
            case "1":
                // cardType=1: 直接使用患者ID查询
                if (StrUtil.isNotBlank(queryDto.getPatient_id())) {
                    params.put("patient_id", queryDto.getPatient_id());
                }
                break;
            case "2":
            case "3":
                // cardType=2,3: 通过身份证号或卡号获取到的患者ID查询
                if (StrUtil.isNotBlank(patientId)) {
                    params.put("patient_id", patientId);
                }
                break;
            case "4":
                // cardType=4: 使用处方号(cfxh)查询
                if (StrUtil.isNotBlank(queryDto.getCfxh())) {
                    params.put("cfxh", queryDto.getCfxh());
                }
                break;
            case "5":
                // cardType=5: 使用从处方号中获取的患者ID和时间范围查询该患者的所有处方
                if (StrUtil.isNotBlank(patientId)) {
                    params.put("patient_id", patientId);
                }
                break;
            default:
                log.warn("不支持的查询类型: {}", queryDto.getCardType());
                break;
        }

        // 通用参数：时间范围
        if (StrUtil.isNotBlank(queryDto.getStartTime())) {
            params.put("start_time", queryDto.getStartTime());
        }

        if (StrUtil.isNotBlank(queryDto.getEndTime())) {
            params.put("end_time", queryDto.getEndTime());
        }

        // 其他可选参数
        if (StrUtil.isNotBlank(queryDto.getFg_dps())) {
            params.put("fg_dps", queryDto.getFg_dps());
        }

        if (StrUtil.isNotBlank(queryDto.getSend_flag())) {
            params.put("send_flag", queryDto.getSend_flag());
        }

        log.info("构建门诊发药明细查询参数完成，cardType={}，参数：{}", queryDto.getCardType(), params);
        return params;
    }

    /**
     * 解析门诊发药明细响应结果
     *
     * @param responseBody 响应体
     * @return 门诊处方明细列表
     */
    private List<OutpatientPrescriptionResponseVo.PrescriptionItem> parseOutpatientResponse(String responseBody) {
        try {
            // 解析JSON响应
            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查响应是否成功
            if (responseJson.containsKey("code")) {
                Integer code = responseJson.getInt("code");
                if (code != null && code != 0) {
                    String message = responseJson.getStr("message", "接口返回异常");
                    throw new BusinessException("接口返回错误：" + message);
                }
            }

            // 直接将dataList转换为OutpatientPrescriptionVo列表
            List<OutpatientPrescriptionVo> dataList = null;
            if (responseJson.containsKey("dataList")) {
                dataList = JSONUtil.toList(responseJson.getJSONArray("dataList"), OutpatientPrescriptionVo.class);
            }

            if (dataList == null || dataList.isEmpty()) {
                log.warn("门诊发药明细数据为空");
                return new ArrayList<>();
            }

            // 转换为PrescriptionItem对象列表
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList = new ArrayList<>();
            for (OutpatientPrescriptionVo voItem : dataList) {
                OutpatientPrescriptionResponseVo.PrescriptionItem prescriptionItem =
                        convertVoToPrescriptionItem(voItem);
                if (prescriptionItem != null) {
                    resultList.add(prescriptionItem);
                }
            }

            return resultList;

        } catch (Exception e) {
            log.error("解析门诊发药明细响应异常", e);
            throw new BusinessException("解析门诊发药明细响应失败：" + e.getMessage(), e);
        }
    }

    /**
     * 将OutpatientPrescriptionVo数据转换为PrescriptionItem对象
     *
     * @param voItem OutpatientPrescriptionVo对象
     * @return PrescriptionItem对象
     */
    private OutpatientPrescriptionResponseVo.PrescriptionItem convertVoToPrescriptionItem(OutpatientPrescriptionVo voItem) {
        try {
            OutpatientPrescriptionResponseVo.PrescriptionItem item =
                    new OutpatientPrescriptionResponseVo.PrescriptionItem();

            // 直接使用实体类字段进行赋值，避免Map取值方式
            item.setMed_list_codg(voItem.getMed_list_codg());
            item.setFixmedins_hilist_id(voItem.getFixmedins_hilist_id());
            item.setFixmedins_hilist_name(voItem.getFixmedins_hilist_name());
            item.setFixmedins_bchno(voItem.getFixmedins_bchno());
            item.setPrsc_dr_name(voItem.getPrsc_dr_name());
            item.setPhar_name(voItem.getPhar_name());
            item.setPhar_prac_cert_no(voItem.getPhar_prac_cert_no());
            item.setMdtrt_sn(voItem.getMdtrt_sn());
            item.setPsn_name(voItem.getPsn_name());
            item.setManu_lotnum(voItem.getManu_lotnum());
            item.setExpy_end(voItem.getExpy_end());

            // 处理数据类型转换：OutpatientPrescriptionVo中是String，PrescriptionItem中是Integer
            item.setRx_flag(parseStringToInteger(voItem.getRx_flag()));
            item.setTrdn_flag(parseStringToInteger(voItem.getTrdn_flag()));

            item.setRxno(voItem.getRxno());
            item.setRx_circ_flag(voItem.getRx_circ_flag());
            item.setRtal_docno(voItem.getRtal_docno());
            item.setStoout_no(voItem.getStoout_no());
            item.setBchno(voItem.getBchno());

            // 处理数据类型转换：OutpatientPrescriptionVo中是Integer，PrescriptionItem中是String
            item.setSel_retn_cnt(parseIntegerToString(voItem.getSel_retn_cnt()));
            item.setMin_sel_retn_cnt(parseIntegerToString(voItem.getMin_sel_retn_cnt()));

            item.setSelRetnUnit(voItem.getSel_retn_unit());
            item.setHisDosUnit(voItem.getHis_dos_unit());
            item.setHisPacUnit(voItem.getHis_pac_unit());
            item.setSel_retn_time(voItem.getSel_retn_time());
            item.setSel_retn_opter_name(voItem.getSel_retn_opter_name());

            // 处理数据类型转换：OutpatientPrescriptionVo中是String，PrescriptionItem中是Integer
            item.setMdtrt_setl_type(parseStringToInteger(voItem.getMdtrt_setl_type()));

            item.setSpec(voItem.getSpec());
            item.setProdentp_name(voItem.getProdentp_name());
            item.setCfxh(voItem.getCfxh());
            item.setCfmxxh(voItem.getCfmxxh());
            item.setSjh(voItem.getSjh());
            item.setPatient_id(voItem.getPatient_id());
            item.setHis_con_ratio(voItem.getHis_con_ratio());

            // 处理数据类型转换：OutpatientPrescriptionVo中是String，PrescriptionItem中是Integer
            item.setSend_flag(parseStringToInteger(voItem.getSend_flag()));
            item.setSend_time(voItem.getSend_time());

            // 复制SaaS拆零接口字段
            item.setDrugCode(voItem.getDrugCode());
            item.setDrugTracCodgs(voItem.getDrugTracCodgs());
            item.setDispCnt(voItem.getDispCnt());
            item.setCurrNum(voItem.getCurrNum());

            // 新增字段映射
            item.setDept_id(voItem.getDept_id());
            item.setDept_name(voItem.getDept_name());
            item.setWindow(voItem.getWindow());

            return item;

        } catch (Exception e) {
            log.error("转换OutpatientPrescriptionVo到PrescriptionItem异常", e);
            return null;
        }
    }

    /**
     * 安全地将字符串转换为整数
     *
     * @param value 字符串值
     * @return 整数值，解析失败时返回null
     */
    private Integer parseStringToInteger(String value) {
        try {
            return StringUtils.isNotEmpty(value) ? Integer.valueOf(value) : null;
        } catch (NumberFormatException e) {
            log.warn("解析字符串到整数失败，值：{}，返回null", value);
            return null;
        }
    }

    /**
     * 安全地将整数转换为字符串
     *
     * @param value 整数值
     * @return 字符串值，null时返回null
     */
    private String parseIntegerToString(Integer value) {
        return value != null ? value.toString() : null;
    }


    /**
     * 处理门诊药品的追溯码库存信息
     * 参考阜阳人医的handleTracCodgStoreCydy方法逻辑
     *
     * @param resultList 门诊处方明细列表
     * @param token      令牌
     */
    private void handleTracCodgStoreForOutpatient(List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList, String token) {
        try {
            // 1. 构建查询药品追溯信息的请求列表
            List<QueryTracDrugRequest> queryTracDrugRequestList = resultList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCfmxxh()) && StringUtils.isNotEmpty(item.getSel_retn_cnt()))
                    .map(item -> QueryTracDrugRequest.builder()
                            .cfxh(item.getCfxh())
                            .cfmxxh(item.getCfmxxh())
                            .drugCode(item.getFixmedins_hilist_id())
                            .dispCnt(parseIntegerSafely(item.getSel_retn_cnt()))
                            .build())
                    .collect(Collectors.toList());

            if (queryTracDrugRequestList.isEmpty()) {
                log.info("没有需要查询追溯信息的门诊药品");
                return;
            }

            // 调用SaaS接口批量查询药品追溯信息
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);

            // 2. 处理追溯标志和转换比
            List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
            for (OutpatientPrescriptionResponseVo.PrescriptionItem item : resultList) {
                if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(item.getCfmxxh());
                    // 设置追溯标志
                    item.setTrdn_flag(Integer.valueOf(queryTracDrug.getIsTrac()));
                    // 设置HIS转换比例
                    item.setHis_con_ratio(String.valueOf(queryTracDrug.getConRatio()));

                    // 如果药品需要追溯，收集需要查询库存的药品
                    if ("1".equals(queryTracDrug.getIsTrac())) {
                        GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder()
                                .cfxh(item.getCfxh())
                                .cfmxxh(item.getCfmxxh())
                                .dispCnt(parseIntegerSafely(item.getMin_sel_retn_cnt()))
                                .drugCode(item.getFixmedins_hilist_id())
                                .build();
                        tracDataList.add(build);
                    }
                }
            }

            // 3. 批量获取追溯码库存信息
            Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
            if (!tracDataList.isEmpty()) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token,
                        GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }

            // 4. 将库存信息填充回药品对象
            for (OutpatientPrescriptionResponseVo.PrescriptionItem item : resultList) {
                if (item.getTrdn_flag() != null && item.getTrdn_flag() == 1 && tracCodgStoreMap.containsKey(item.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(item.getCfmxxh());
                    item.setDrugCode(tracCodgStoreData.getDrugCode());
                    item.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    item.setDispCnt(tracCodgStoreData.getDispCnt());
                    item.setCurrNum(tracCodgStoreData.getCurrNum());
                    item.setTracCodgStore(tracCodgStoreData);
                }
            }

            log.info("门诊药品追溯码库存信息处理完成，共处理{}条数据，其中{}条需要追溯", resultList.size(), tracDataList.size());

        } catch (Exception e) {
            log.error("处理门诊药品追溯码库存信息异常", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理门诊任务状态和数据过滤
     * 参考阜阳人医门诊处方查询中的任务状态处理逻辑
     * 过滤掉已完成扫码任务的药品信息，防止后续业务重复上传造成脏数据
     *
     * @param resultList 门诊处方明细列表
     */
    private void filterByTaskStatusForOutpatient(List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList) {
        if (resultList == null || resultList.isEmpty()) {
            return;
        }

        // 处理每条处方数据,过滤掉已完成扫码任务的药品信息,防止后续业务重复上传造成脏数据
        Iterator<OutpatientPrescriptionResponseVo.PrescriptionItem> iterator = resultList.iterator();
        while (iterator.hasNext()) {
            OutpatientPrescriptionResponseVo.PrescriptionItem dispenseInfo = iterator.next();
            String outPresId = dispenseInfo.getCfxh();
            if (StringUtils.isEmpty(outPresId)) {
                continue;
            }

            // 1. 查询是否存在已完成且未删除的任务
            LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
            completedTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode()) // 已完成
                    .eq(YsfStoTcTask::getDelFlag, "0"); // 未删除
            Long completedTaskCount = ysfStoTcTaskMapper.selectCount(completedTaskQuery);

            // 2. 如果存在已完成的任务，则过滤掉该处方数据
            if (completedTaskCount > 0) {
                iterator.remove();
                continue;
            }

            // 3. 如果不存在已完成的任务，则查询最新的待处理或已失效任务
            LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
            latestTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode())) // 待处理或已失效
                    .eq(YsfStoTcTask::getDelFlag, "0") // 未删除
                    .orderByDesc(YsfStoTcTask::getIdTask)
                    .last("limit 1"); // 按创建时间降序

            YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

            // 4. 如果找到这样的任务，则在处方对象中追加任务相关字段
            if (latestTask != null) {
                // 使用实体类中新增的字段
                dispenseInfo.setTaskIdDps(latestTask.getIdTask() != null ?
                        latestTask.getIdTask().toString() : null);
                dispenseInfo.setTaskFgStatusDps(latestTask.getFgStatus());
                dispenseInfo.setTaskScanTimeDps(latestTask.getCreateTime() != null ?
                        latestTask.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            }
        }
    }

    /**
     * 构建住院请求参数
     *
     * @param queryDto 查询DTO
     * @return 请求参数Map
     */
    private Map<String, Object> buildRequestParams(InpatientPrescriptionQueryDto queryDto) {
        Map<String, Object> params = new HashMap<>();

        // 根据需求描述中的curl示例构建参数
        if (StrUtil.isNotBlank(queryDto.getStartTime())) {
            params.put("start_time", queryDto.getStartTime());
        }

        if (StrUtil.isNotBlank(queryDto.getEndTime())) {
            params.put("end_time", queryDto.getEndTime());
        }

        if (StrUtil.isNotBlank(queryDto.getRecordId())) {
            params.put("record_id", queryDto.getRecordId());
        }

        // fg_dps 参数必填
        params.put("fg_dps", StrUtil.isNotBlank(queryDto.getFgDps()) ? queryDto.getFgDps() : "0");

        // 其他可选参数
        if (StrUtil.isNotBlank(queryDto.getFyyf())) {
            params.put("fyyf", queryDto.getFyyf());
        }

        if (StrUtil.isNotBlank(queryDto.getDeptId())) {
            params.put("dept_id", queryDto.getDeptId());
        }

        if (StrUtil.isNotBlank(queryDto.getPatWardId())) {
            params.put("pat_ward_id", queryDto.getPatWardId());
        }

        return params;
    }

    /**
     * 同步药品字典
     *
     * @return 同步结果
     */
    public ApiResult<String> syncDrugDictionary() {
        log.info("开始同步HIS药品字典到DMH平台");

        try {
            // 获取SaaS用户信息
            AccessTokenReponse userInfo = SaasHttpUtil.getAccessToken(DrugDictAccountConstant.user, DrugDictAccountConstant.password);

            // 1. 分页拉取HIS系统全量药品数据
            List<HisDrugCatalogResponse.HisDrugCatalogItem> allDrugList = fetchAllDrugsFromHIS();
            if (allDrugList.isEmpty()) {
                log.warn("从HIS系统未获取到任何药品数据");
                return ApiResult.success("未获取到药品数据，同步完成");
            }

            log.info("从HIS系统共获取到{}条药品数据", allDrugList.size());

            // 2. 数据转换：将HIS数据转换为DMH平台要求的格式
            List<HisDrugInfoSaasApiData> convertedDrugList = convertHisDrugsToDmhFormat(allDrugList);
            log.info("成功转换{}条药品数据", convertedDrugList.size());

            // 3. 分批上传到DMH平台
            int batchSize = 200; // 每批200条
            int totalBatches = (int) Math.ceil((double) convertedDrugList.size() / batchSize);
            int uploadedCount = 0;

            for (int i = 0; i < totalBatches; i++) {
                int fromIndex = i * batchSize;
                int toIndex = Math.min((i + 1) * batchSize, convertedDrugList.size());
                List<HisDrugInfoSaasApiData> batchData = convertedDrugList.subList(fromIndex, toIndex);

                // 上传单批数据
                boolean batchSuccess = uploadDrugBatchToDmh(batchData, i + 1, totalBatches, userInfo);
                if (batchSuccess) {
                    uploadedCount += batchData.size();
                    log.info("第{}/{}批药品数据上传成功，当前批次{}条，累计上传{}条",
                            i + 1, totalBatches, batchData.size(), uploadedCount);
                } else {
                    log.error("第{}/{}批药品数据上传失败", i + 1, totalBatches);
                    return ApiResult.error(String.format("第%d批药品数据上传失败，已成功上传%d条", i + 1, uploadedCount));
                }

                // 避免请求过于频繁，添加间隔
                if (i < totalBatches - 1) {
                    try {
                        Thread.sleep(1000); // 1秒间隔
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("同步过程中断");
                        break;
                    }
                }
            }

            String successMessage = String.format("药品字典同步完成！共处理%d条数据，成功上传%d条",
                    convertedDrugList.size(), uploadedCount);
            log.info(successMessage);
            return ApiResult.success(successMessage);

        } catch (Exception e) {
            log.error("同步药品字典异常", e);
            return ApiResult.error("同步药品字典失败：" + e.getMessage());
        }
    }

    /**
     * 分页拉取HIS系统全量药品数据
     *
     * @return 全量药品数据列表
     */
    private List<HisDrugCatalogResponse.HisDrugCatalogItem> fetchAllDrugsFromHIS() {
        List<HisDrugCatalogResponse.HisDrugCatalogItem> allDrugList = new ArrayList<>();
        int currentPage = 1;
        int totalPages = 1;

        do {
            try {
                log.info("开始拉取HIS药品数据，第{}/{}页", currentPage, totalPages);

                // 构建请求参数
                Map<String, Object> requestParams = new HashMap<>();
                requestParams.put("pageNo", currentPage);
                requestParams.put("pageSize", 200); // 根据文档示例，每页11条


                // 发起HTTP请求
                String responseBody = HttpUtil.get(hangChuangConfig.getHisDrugCatalogUrl(), requestParams);

                // 解析响应结果
                HisDrugCatalogResponse response = HttpRequestUtil.parseResponseToObject(responseBody, HisDrugCatalogResponse.class);

                if (response == null) {
                    log.error("解析HIS药品目录响应失败，第{}页", currentPage);
                    break;
                }

                // 更新总页数
                if (response.getTotalPageCount() != null) {
                    totalPages = response.getTotalPageCount();
                }

                // 添加当前页的数据
                if (response.getResultList() != null && !response.getResultList().isEmpty()) {
                    allDrugList.addAll(response.getResultList());
                    log.info("第{}/{}页获取到{}条药品数据，累计{}条",
                            currentPage, totalPages, response.getResultList().size(), allDrugList.size());
                } else {
                    log.warn("第{}页未获取到药品数据", currentPage);
                }

                currentPage++;

            } catch (Exception e) {
                log.error("拉取HIS药品数据异常，第{}页", currentPage, e);
                break;
            }

        } while (currentPage <= totalPages);

        log.info("HIS药品数据拉取完成，共获取{}条数据", allDrugList.size());
        return allDrugList;
    }

    /**
     * 将HIS药品数据转换为DMH平台要求的格式
     *
     * @param hisDrugList HIS药品数据列表
     * @return 转换后的药品数据列表
     */
    private List<HisDrugInfoSaasApiData> convertHisDrugsToDmhFormat(List<HisDrugCatalogResponse.HisDrugCatalogItem> hisDrugList) {
        log.info("开始转换药品数据格式，共{}条待转换", hisDrugList.size());

        List<HisDrugInfoSaasApiData> convertedList = new ArrayList<>();
        int convertedCount = 0;
        int skippedCount = 0;

        for (HisDrugCatalogResponse.HisDrugCatalogItem hisItem : hisDrugList) {
            try {
                HisDrugInfoSaasApiData dmhItem = new HisDrugInfoSaasApiData();

                // 按照文档映射规则进行转换

                // 1. hisDrugId = hisDrugCode + "-" + hisDrugManufacturerCode（核心规则）
                if (StringUtils.isNotEmpty(hisItem.getHisDrugCode()) &&
                        StringUtils.isNotEmpty(hisItem.getHisDrugManufacturerCode())) {
                    dmhItem.setHisDrugId(hisItem.getHisDrugCode() + "-" + hisItem.getHisDrugManufacturerCode());
                } else {
                    log.warn("药品代码或生产企业编码为空，跳过该药品：hisDrugCode={}, hisDrugManufacturerCode={}",
                            hisItem.getHisDrugCode(), hisItem.getHisDrugManufacturerCode());
                    skippedCount++;
                    continue;
                }

                // 2. hisDrugName：直接映射
                dmhItem.setHisDrugName(hisItem.getHisDrugName());

                // 3. hisDrugCountryCode：映射自drugUnino
                dmhItem.setHisDrugCountryCode(hisItem.getDrugUnino());

                // 4. hisDrugCountryName：直接映射
                dmhItem.setHisDrugCountryName(hisItem.getHisDrugCountryName());

                // 5. hisDrugSpec：直接映射
                dmhItem.setHisDrugSpec(hisItem.getHisDrugSpec());

                // 6. hisPackUnit：映射自hisDrugUnit（包装单位）
                dmhItem.setHisPackUnit(hisItem.getHisDrugUnit());

                // 7. hisDrugManufacturerCode：直接映射
                dmhItem.setHisDrugManufacturerCode(hisItem.getHisDrugManufacturerCode());

                // 8. hisDrugManufacturerName：直接映射
                dmhItem.setHisDrugManufacturerName(hisItem.getHisDrugManufacturerName());

                // 9. hisPurchaseUnit：建议映射（采购单位通常就是包装单位）
                dmhItem.setHisPurchaseUnit(hisItem.getHisDrugUnit());

                // 10. hisDoseForm：映射自dosform
                dmhItem.setHisDoseForm(hisItem.getDosform());

                // 11. hisApprovalNum：映射自aprvno（可能为空）
                dmhItem.setHisApprovalNum(hisItem.getAprvno());

                // 12. hisPacUnit：映射自hisPacUnit（最小包装单位）
                dmhItem.setHisPacUnit(hisItem.getHisPacUnit());

                // 13. hisPac：映射自hisPac（字符型）
                dmhItem.setHisPac(hisItem.getHisPac());

                // 14. hisConRatio：映射自hisPac（数值型，转换比即为包装数量）
                if (StringUtils.isNotEmpty(hisItem.getHisPac())) {
                    try {
                        dmhItem.setHisConRatio(new BigDecimal(hisItem.getHisPac()));
                    } catch (NumberFormatException e) {
                        log.warn("转换比解析失败，使用默认值1：hisPac={}", hisItem.getHisPac());
                        dmhItem.setHisConRatio(BigDecimal.ONE);
                    }
                } else {
                    dmhItem.setHisConRatio(BigDecimal.ONE);
                }

                // 15. delFlag：设置固定值0（数据有效）
                dmhItem.setDelFlag(0);

                // 其他字段设置为null或留空
                dmhItem.setHisEnterpriseCode(null);
                dmhItem.setHisEnterpriseName(null);
                dmhItem.setHisPurchasePrice(hisItem.getHisPurchasePrice());
                dmhItem.setHisDosUnit(null);
                dmhItem.setWholeQuantity(null);
                dmhItem.setHisDiscRate(null);
                dmhItem.setMemo(null);

                convertedList.add(dmhItem);
                convertedCount++;

            } catch (Exception e) {
                log.error("转换药品数据异常，跳过该药品：{}", JSONUtil.toJsonStr(hisItem), e);
                skippedCount++;
            }
        }

        log.info("药品数据转换完成，成功转换{}条，跳过{}条", convertedCount, skippedCount);
        return convertedList;
    }

    /**
     * 上传单批药品数据到DMH平台
     *
     * @param batchData    批次数据
     * @param batchIndex   批次索引
     * @param totalBatches 总批次数
     * @param userInfo     用户信息
     * @return 上传是否成功
     */
    private boolean uploadDrugBatchToDmh(List<HisDrugInfoSaasApiData> batchData, int batchIndex,
                                         int totalBatches, AccessTokenReponse userInfo) {
        try {
            // 构建请求对象
            HisDrugInfoSaasRequest request = new HisDrugInfoSaasRequest();
            request.setMedicalCode(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            request.setMedicalName(NhsaAccountConstant.getNhsaAccount().getMedicalName());
            request.setDataList(batchData);

            // 生成唯一的requestID
            String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            String requestID = String.format("DRUG-SYNC-%s-B%03d", timestamp, batchIndex);
            request.setRequestID(requestID);

            log.info("开始上传第{}/{}批药品数据到DMH平台，批次大小：{}，requestID：{}",
                    batchIndex, totalBatches, batchData.size(), requestID);

            // 调用SaaS接口上传
            SaasCommmonListResponse<UploadHisDrugInfoResponse> response =
                    SaasHttpUtil.uploadHisDrugInfo(userInfo.getAuthorization(), request);

            if (Objects.equals(response.getReturnCode(), 0)) {
                log.info("第{}/{}批药品数据上传成功，requestID：{}，响应：{}",
                        batchIndex, totalBatches, requestID, JSONUtil.toJsonStr(response));
                return true;
            } else {
                log.error("第{}/{}批药品数据上传失败，requestID：{}，响应：{}", batchIndex, totalBatches, requestID, JSONUtil.toJsonStr(response));
                return false;
            }

        } catch (Exception e) {
            log.error("第{}/{}批药品数据上传异常", batchIndex, totalBatches, e);
            return false;
        }
    }

    /**
     * 查询今天出院结算病人
     * 实现完整的出院患者药品追溯码补录和上传流程
     *
     * @param startDate 开始日期 yyyy-MM-dd
     * @param endDate   结束日期 yyyy-MM-dd
     * @return 处理结果
     */
    public ApiResult<String> queryTodayDischargedPatients(String startDate, String endDate) {
        log.info("开始处理出院患者药品追溯码补录，时间范围：{} 至 {}", startDate, endDate);

        try {
            // Step 1: 身份认证 - 使用住院账号获取访问token
            AccessTokenReponse tokenResponse = authenticateInpatientAccount();
            if (tokenResponse == null || tokenResponse.getAuthorization() == null) {
                return ApiResult.error("身份认证失败，无法获取访问token");
            }
            String token = tokenResponse.getAuthorization();
            log.info("身份认证成功，获取到访问token");

            // Step 2: 查询出院患者列表
            List<InpatientSettlementVo> dischargedPatients = queryDischargedPatientsList(startDate, endDate);
            if (dischargedPatients.isEmpty()) {
                log.info("指定时间范围内无出院患者");
                return ApiResult.success("指定时间范围内无出院患者");
            }
            log.info("查询到出院患者数量：{}", dischargedPatients.size());

            // Step 3: 循环处理每个出院患者
            int totalPatients = dischargedPatients.size();
            int successPatients = 0;
            int failedPatients = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (InpatientSettlementVo patient : dischargedPatients) {
                try {

                    // 检查患者的取消标志，只处理未取消的患者数据
                    if (patient.getCancelFlag() != null && patient.getCancelFlag().equals("1")) {
                        log.info("患者 {} 撤销结算，暂时未实现撤销结算的业务,先跳过处理", patient.getPatientName());
                        continue;
                    }


                    log.info("开始处理出院患者：{} (住院号：{})", patient.getPatientName(), patient.getPatInHosId());

                    // 处理单个患者的药品追溯码补录
                    boolean patientResult = processDischargedPatient(patient, token);

                    if (patientResult) {
                        successPatients++;
                        log.info("患者 {} 处理成功", patient.getPatientName());
                    } else {
                        failedPatients++;
                        log.warn("患者 {} 处理失败", patient.getPatientName());
                        errorMessages.append("患者").append(patient.getPatientName()).append("处理失败; ");
                    }

                } catch (Exception e) {
                    failedPatients++;
                    log.error("处理患者 {} 时发生异常", patient.getPatientName(), e);
                    errorMessages.append("患者").append(patient.getPatientName()).append("异常：").append(e.getMessage())
                            .append("; ");
                }
            }

            // Step 4: 返回处理结果
            String resultMessage = String.format("出院患者药品追溯码补录完成。总患者数：%d，成功：%d，失败：%d",
                    totalPatients, successPatients, failedPatients);

            if (failedPatients > 0) {
                resultMessage += "。失败详情：" + errorMessages.toString();
                log.warn(resultMessage);
                return ApiResult.success(resultMessage); // 部分成功也返回success，但包含失败信息
            } else {
                log.info(resultMessage);
                return ApiResult.success(resultMessage);
            }

        } catch (Exception e) {
            log.error("处理出院患者药品追溯码补录时发生异常", e);
            return ApiResult.error("处理出院患者药品追溯码补录失败：" + e.getMessage());
        }
    }

    /**
     * 住院账号身份认证
     * 使用InpatientAccountConstant中定义的账号信息进行登录
     *
     * @return 访问token响应对象
     */
    private AccessTokenReponse authenticateInpatientAccount() {
        try {
            log.info("开始住院账号身份认证，账号：{}", InpatientAccountConstant.user);

            AccessTokenReponse tokenResponse = SaasHttpUtil.getAccessToken(
                    InpatientAccountConstant.user,
                    InpatientAccountConstant.password
            );

            if (tokenResponse == null) {
                log.error("身份认证失败：响应为空");
                return null;
            }

            if (tokenResponse.getReturnCode() != 0) {
                log.error("身份认证失败，返回码：{}，消息：{}",
                        tokenResponse.getReturnCode(), tokenResponse.getReturnMsg());
                return null;
            }

            if (StrUtil.isBlank(tokenResponse.getAuthorization())) {
                log.error("身份认证失败：访问token为空");
                return null;
            }

            log.info("住院账号身份认证成功，用户：{}", tokenResponse.getNickName());
            return tokenResponse;

        } catch (Exception e) {
            log.error("住院账号身份认证异常", e);
            return null;
        }
    }

    /**
     * 查询出院患者列表
     * 调用HIS接口获取指定时间范围内的出院患者信息
     *
     * @param startDate 开始日期 yyyy-MM-dd
     * @param endDate   结束日期 yyyy-MM-dd
     * @return 出院患者列表
     */
    private List<InpatientSettlementVo> queryDischargedPatientsList(String startDate, String endDate) {
        try {
            log.info("开始查询出院患者列表，时间范围：{} - {}", startDate, endDate);

            // 获取出院患者列表接口URL
            String requestUrl = hangChuangConfig.getInpatientSettlementUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executeGetRequest(requestUrl + "?start_time=" + startDate + "&end_time=" + endDate,"查询出院患者列表");

            // 解析响应结果
            List<InpatientSettlementVo> resultList = HttpRequestUtil.parseResponseToList(responseBody, InpatientSettlementVo.class);

            log.info("查询出院患者列表成功，返回{}条数据", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("查询出院患者列表异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理单个出院患者的药品追溯码补录
     * 实现完整的业务流程：查询明细、比对差异、补录追溯码、上传数据
     *
     * @param patient 出院患者信息
     * @param token   访问token
     * @return 处理是否成功
     */
    private boolean processDischargedPatient(InpatientSettlementVo patient, String token) {
        String patientName = patient.getPatientName();
        try {
            // Step 1: 查询该患者的住院发药明细
            List<InPatientDispenseDetailBindScatteredVo> patientDispenseDetails = queryPatientDispenseDetails(patient);


            if (patientDispenseDetails.isEmpty()) {
                log.info("患者 {} 无住院发药明细", patientName);
                return true; // 无数据不算失败
            }

            log.info("患者 {} 查询到住院发药明细 {} 条", patientName, patientDispenseDetails.size());

            // Step 2: 查询已保存的3505业务数据
            List<Nhsa3505> existing3505Data = queryExisting3505Data(patient);
            log.info("患者 {} 已存在3505数据 {} 条", patientName, existing3505Data.size());

            // Step 3: 比对差异数据，找出需要处理的记录
            ProcessingDataResult processingResult = compareAndIdentifyProcessingData(
                    patientDispenseDetails, existing3505Data);

            log.info("患者 {} 数据比对结果：需要更新追溯码 {} 条，需要新增 {} 条",
                    patientName,
                    processingResult.getUpdateTracCodeList().size(),
                    processingResult.getInsertNewList().size());

            // Step 4: 处理需要更新追溯码的数据
            if (!processingResult.getUpdateTracCodeList().isEmpty()) {
                boolean updateResult = processUpdateTracCodeData(processingResult.getUpdateTracCodeList(), token);
                if (!updateResult) {
                    log.error("患者 {} 更新追溯码数据失败", patientName);
                    return false;
                }
            }

            // Step 5: 处理需要新增的数据
            if (!processingResult.getInsertNewList().isEmpty()) {
                boolean insertResult = processInsertNewData(processingResult.getInsertNewList(), token);
                if (!insertResult) {
                    log.error("患者 {} 新增数据失败", patientName);
                    return false;
                }
            }

            // Step 6: 确认拆零信息
            boolean confirmResult = confirmDispenseData(processingResult.getAllProcessedData(), token);
            if (!confirmResult) {
                log.error("患者 {} 确认拆零信息失败", patientName);
                return false;
            }

            // Step 7: 上传3505接口
            boolean uploadResult = upload3505Data(patient);
            if (!uploadResult) {
                log.error("患者 {} 上传3505数据失败", patientName);
                return false;
            }

            log.info("患者 {} 处理完成", patientName);
            return true;

        } catch (Exception e) {
            log.error("处理患者 {} 时发生异常", patientName, e);
            return false;
        }
    }

    /**
     * 查询患者的住院发药明细
     * 根据患者的住院ID查询所有相关的发药记录
     *
     * @param patient 出院患者信息
     * @return 住院发药明细列表
     */
    private List<InPatientDispenseDetailBindScatteredVo> queryPatientDispenseDetails(InpatientSettlementVo patient) {
        try {
            log.info("开始查询患者 {} 的住院发药明细", patient.getPatientName());

            // 构建查询参数 - 使用住院ID作为记录ID查询
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("pat_in_hos_id", patient.getPatInHosId());

            // 获取出院患者列表接口URL
            String requestUrl = hangChuangConfig.getInpatientDispenseDetailUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "查询住院发药明细");

            // 解析响应结果
            List<InPatientDispenseDetailBindScatteredVo> resultList = HttpRequestUtil.parseResponseToList(responseBody, InPatientDispenseDetailBindScatteredVo.class);

            log.info("查询住院发药明细成功，返回{}条数据", resultList.size());
            return resultList;


        } catch (Exception e) {
            log.error("查询患者 {} 发药明细his接口异常", patient.getPatientName(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询已存在的3505业务数据
     * 根据患者信息查询已保存在nhsa_3505表中的数据
     *
     * @param patient 出院患者信息
     * @return 已存在的3505数据列表
     */
    private List<Nhsa3505> queryExisting3505Data(InpatientSettlementVo patient) {
        String patientName = patient.getPatientName();
        try {
            log.info("开始查询患者 {} 的已存在3505数据", patientName);

            // 构建查询条件
            LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Nhsa3505::getMedicalCode, NhsaAccountConstant.getNhsaAccount().getMedicalCode());

            // 根据就医流水号查询（如果有的话）
            if (StrUtil.isNotBlank(patient.getMdtrtSn())) {
                queryWrapper.eq(Nhsa3505::getMdtrtSn, patient.getMdtrtSn());
            } else {
                // 如果没有就医流水号，根据患者姓名查询
                queryWrapper.eq(Nhsa3505::getPatientId, patient.getPatInHosId());
            }

            // 只查询住院数据
            queryWrapper.eq(Nhsa3505::getSdDps, SdDpsEnum.INPATIENT); // 1-住院

            List<Nhsa3505> existingData = nhsa3505Service.list(queryWrapper);
            log.info("患者 {} 查询到已存在3505数据 {} 条", patientName, existingData.size());

            return existingData;

        } catch (Exception e) {
            log.error("查询患者 {} 已存在3505数据异常", patientName, e);
            return new ArrayList<>();
        }
    }

    /**
     * 比对差异数据，识别需要处理的记录
     * 将发药明细与已存在的3505数据进行比对，找出需要更新和新增的数据
     *
     * @param dispenseDetails  发药明细列表
     * @param existing3505Data 已存在的3505数据列表
     * @return 数据处理结果
     */
    private ProcessingDataResult compareAndIdentifyProcessingData(
            List<InPatientDispenseDetailBindScatteredVo> dispenseDetails,
            List<Nhsa3505> existing3505Data) {

        ProcessingDataResult result = ProcessingDataResult.builder().build();

        try {
            log.info("开始比对差异数据，发药明细：{}条，已存在3505数据：{}条",
                    dispenseDetails.size(), existing3505Data.size());

            // 将已存在的3505数据转换为Map，便于快速查找
            // 使用fixmedins_bchno作为key进行匹配
            Map<String, Nhsa3505> existing3505Map = existing3505Data.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getFixmedinsBchno()))
                    .collect(Collectors.toMap(
                            Nhsa3505::getFixmedinsBchno,
                            Function.identity(),
                            (existing, replacement) -> existing // 如果有重复key，保留第一个
                    ));

            // 遍历发药明细，进行比对
            for (InPatientDispenseDetailBindScatteredVo dispenseDetail : dispenseDetails) {
                try {
                    // 检查必要字段
                    if (StrUtil.isBlank(dispenseDetail.getFixmedinsBchno())) {
                        log.warn("发药明细缺少定点医疗机构批次号，跳过处理：{}", dispenseDetail.getRecordDetailId());
                        result.incrementSkippedCount();
                        continue;
                    }

                    String batchNo = dispenseDetail.getFixmedinsBchno();
                    Nhsa3505 existing3505 = existing3505Map.get(batchNo);

                    if (existing3505 != null) {
                        // 数据已存在于3505表中，检查是否需要更新追溯码
                        if (StrUtil.isBlank(existing3505.getDrugTracInfo())) {
                            // 缺少追溯码，需要更新
                            result.addUpdateTracCodeData(dispenseDetail);
                            log.info("发现需要更新追溯码的数据：{}", batchNo);
                        } else {
                            // 数据完整，跳过处理
                            result.incrementSkippedCount();
                            log.info("数据已完整，跳过处理：{}", batchNo);
                        }
                    } else {
                        // 数据不存在于3505表中，需要新增
                        result.addInsertNewData(dispenseDetail);
                        log.info("发现需要新增的数据：{}", batchNo);
                    }

                } catch (Exception e) {
                    log.error("处理发药明细时发生异常：{}", dispenseDetail.getRecordDetailId(), e);
                    result.incrementSkippedCount();
                }
            }

            log.info("数据比对完成：{}", result.getSummary());
            return result;

        } catch (Exception e) {
            log.error("比对差异数据时发生异常", e);
            return result;
        }
    }

    /**
     * 处理需要更新追溯码的数据
     * 从拆零池获取追溯码信息，更新到3505表中
     *
     * @param updateList 需要更新的数据列表
     * @param token      访问token
     * @return 处理是否成功
     */
    private boolean processUpdateTracCodeData(List<InPatientDispenseDetailBindScatteredVo> updateList, String token) {
        try {
            log.info("开始处理需要更新追溯码的数据，数量：{}", updateList.size());

            // Step 1: 从拆零池获取追溯码信息
            List<InPatientDispenseDetailBindScatteredVo> enrichedList =
                    handleTracCodgStoreForInpatient(updateList, token);

            // Step 2: 更新3505表中的追溯码信息
            int successCount = 0;
            int failCount = 0;

            for (InPatientDispenseDetailBindScatteredVo item : enrichedList) {
                try {
                    // 检查是否获取到追溯码
                    if ("1".equals(item.getTrdnFlag()) && item.getDrugTracCodgs() != null && !item.getDrugTracCodgs().isEmpty()) {
                        // 构建追溯码字符串
                        String drugTracInfo = String.join(",", item.getDrugTracCodgs());

                        // 更新3505表
                        nhsa3505Service.updateDrugTraceabilityInfo(item.getFixmedinsBchno(), drugTracInfo);

                        successCount++;
                        log.debug("成功更新追溯码：{}", item.getCfmxxh());
                    } else {
                        log.warn("未获取到追溯码信息，跳过更新：{}", item.getCfmxxh());
                        failCount++;
                    }

                } catch (Exception e) {
                    log.error("更新追溯码失败：{}", item.getCfmxxh(), e);
                    failCount++;
                }
            }

            log.info("更新追溯码处理完成，成功：{}，失败：{}", successCount, failCount);
            return failCount == 0; // 只有全部成功才返回true

        } catch (Exception e) {
            log.error("处理更新追溯码数据时发生异常", e);
            return false;
        }
    }

    /**
     * 处理需要新增的数据
     * 从拆零池获取追溯码信息，插入到3505业务表中，以及发药单主子表和追溯码主子表中
     *
     * @param insertList 需要新增的数据列表
     * @param token      访问token
     * @return 处理是否成功
     */
    private boolean processInsertNewData(List<InPatientDispenseDetailBindScatteredVo> insertList, String token) {
        try {
            log.info("开始处理需要新增的数据，数量：{}", insertList.size());

            // Step 1: 从拆零池获取追溯码信息
            List<InPatientDispenseDetailBindScatteredVo> enrichedList =
                    handleTracCodgStoreForInpatient(insertList, token);

            if (enrichedList.isEmpty()) {
                log.info("从拆零池获取的数据为空，跳过后续处理");
                return true;
            }

            // Step 2: 保存到3505表
            nhsa3505Service.saveInpatientDataToNhsa3505Async(enrichedList);

            // Step 3: 保存到发药单主子表和追溯码主子表（不包含3505表，避免重复保存）
            // 调用专门的数据持久化服务，避免Service层循环依赖
            // 使用InpatientAccountConstant中定义的住院药房账号信息
            dataPersistenceService.saveDispenseDataOnlyByCredentials(enrichedList, 
                InpatientAccountConstant.user, InpatientAccountConstant.password, 1L);

            log.info("新增数据处理完成，数量：{}", enrichedList.size());
            return true;

        } catch (Exception e) {
            log.error("处理新增数据时发生异常", e);
            return false;
        }
    }

    /**
     * 确认拆零信息
     * 调用SaaS接口确认发药数据
     *
     * @param processedDataList 已处理的数据列表
     * @param token             访问token
     * @return 确认是否成功
     */
    private boolean confirmDispenseData(List<InPatientDispenseDetailBindScatteredVo> processedDataList, String token) {
        try {
            log.info("开始确认拆零信息，数量：{}", processedDataList.size());

            // 过滤出需要确认的拆零药品
            List<InPatientDispenseDetailBindScatteredVo> confirmList = processedDataList.stream()
                    .filter(item -> "1".equals(item.getTrdnFlag()))
                    .collect(Collectors.toList());

            if (confirmList.isEmpty()) {
                log.info("无需要确认的拆零药品");
                return true;
            }

            // 构建确认发药接口请求参数
            List<ConfirmDispDrugDataRequest> requestDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo record : confirmList) {
                ConfirmDispDrugDataRequest request = new ConfirmDispDrugDataRequest();
                request.setDrugCode(record.getHisDrugCode());
                request.setDispCnt(record.getSelRetnCnt());
                request.setCfxh(record.getCfxh());
                request.setCfmxxh(record.getCfmxxh());
                requestDataList.add(request);
            }

            if (!requestDataList.isEmpty()) {
                // 构建批次请求
                ConfirmDispDrugRequest confirmRequest = new ConfirmDispDrugRequest();
                confirmRequest.setDataList(requestDataList);

                // 调用confirmDispDrug接口
                SaasHttpUtil.confirmDispDrug(token, confirmRequest);
                log.info("确认拆零信息成功，确认数量：{}", requestDataList.size());
            }

            return true;

        } catch (Exception e) {
            log.error("确认拆零信息时发生异常", e);
            return false;
        }
    }

    /**
     * 上传3505数据到医保平台
     * 调用现有的上传方法处理患者相关的3505数据
     *
     * @param patient 出院患者信息
     * @return 上传是否成功
     */
    private boolean upload3505Data(InpatientSettlementVo patient) {
        try {
            log.info("开始上传患者 {} 的3505数据", patient.getPatientName());

            // 调用现有的上传方法
            // 这里可以根据患者信息筛选特定的数据进行上传
            // 由于现有方法是全量上传，我们直接调用
            List<Nhsa3505> list = nhsa3505Service.lambdaQuery()
                                .eq(Nhsa3505::getMdtrtSn, patient.getMdtrtSn())
                                .eq(Nhsa3505::getSdDps, SdDpsEnum.INPATIENT)
                                .ne(Nhsa3505::getDrugTracInfo, "")
                                .isNotNull(Nhsa3505::getDrugTracInfo)
                                .eq(Nhsa3505::getHsaSyncStatus, "0")
                                .list();;

            if (list.isEmpty()) {
                log.info("患者 {} 无需要上传的3505数据", patient.getPatientName());
                return true;
            }

            ApiResult<String> result = nhsa3505Service.processUpload(list, false);
            if (result.getCode() != 200) {
                log.error("患者 {} 上传3505数据失败", patient.getPatientName());
                return false;
            }

            log.info("患者 {} 的3505数据上传完成", patient.getPatientName());
            return true;

        } catch (Exception e) {
            log.error("上传患者 {} 的3505数据时发生异常", patient.getPatientName(), e);
            return false;
        }
    }
}
