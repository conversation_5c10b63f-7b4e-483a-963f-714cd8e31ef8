package com.zsm.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 出院患者结算信息VO
 * 用于封装出院患者列表查询接口的响应数据
 * 
 * <AUTHOR>
 * @date 2025/8/3 21:00
 */
@NoArgsConstructor
@Data
@Schema(description = "出院患者结算信息")
public class InpatientSettlementVo {

    @Schema(description = "住院ID", example = "1968143")
    @JsonProperty("pat_in_hos_id")
    private String patInHosId;

    @Schema(description = "患者ID", example = "8221633")
    @JsonProperty("patient_id")
    private String patientId;

    @Schema(description = "患者姓名", example = "陈XX")
    @JsonProperty("patient_name")
    private String patientName;

    @Schema(description = "结算时间", example = "2025-07-31 09:12:14")
    @JsonProperty("sel_time")
    private String selTime;

    @Schema(description = "医保登记流水号", example = "34122025072791322685")
    @JsonProperty("mdtrt_sn")
    private String mdtrtSn;

    @Schema(description = "医保结算ID", example = "34122025073171169478")
    @JsonProperty("setl_id")
    private String setlId;

    @Schema(description = "结算标志 0出院结算 1撤销结算", example = "0", allowableValues = {"0", "1"})
    @JsonProperty("cancel_flag")
    private String cancelFlag;

    @Schema(description = "撤销时间", example = "2025-08-03 15:08:38")
    @JsonProperty("cancel_time")
    private String cancelTime;
}
